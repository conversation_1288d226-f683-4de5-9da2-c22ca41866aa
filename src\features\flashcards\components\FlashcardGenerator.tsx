import React, { useState, useEffect, useCallback } from 'react';
import {
  Documento,
  Flashcard,
  ColeccionFlashcards,
  crearColeccionFlashcards,
  guardarFlashcards,
  obtenerColeccionesFlashcards
} from '../../../lib/supabase/index';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { flashcardFormSchema } from '../../../lib/formSchemas';
import { useBackgroundGeneration } from '@/hooks/useBackgroundGeneration';
import { useBackgroundTasks } from '@/contexts/BackgroundTasksContext';
import { useTaskResults } from '@/hooks/useTaskResults';
import { toast } from 'react-hot-toast';
import { useFreeAccountGuard } from '@/components/ui/FreeAccountGuard';
import { useAuth } from '@/contexts/AuthContext';
import { FlashcardRecommendationsModal } from '@/components/ui/RecommendationsModal';
import { useUserPlan } from '@/hooks/useUserPlan';
import { usePlanLimits } from '@/hooks/usePlanLimits';
import { PLAN_CONFIGURATIONS } from '@/config/plans';
import { FiLock, FiArrowUp } from 'react-icons/fi';
import Link from 'next/link';

interface FlashcardGeneratorProps {
  documentosSeleccionados: Documento[];
}

interface FlashcardGenerada {
  pregunta: string;
  respuesta: string;
}

export default function FlashcardGenerator({ documentosSeleccionados }: FlashcardGeneratorProps) {
  const [tituloColeccion, setTituloColeccion] = useState('');
  const [descripcionColeccion, setDescripcionColeccion] = useState('');
  const [flashcardsGeneradas, setFlashcardsGeneradas] = useState<FlashcardGenerada[]>([]);
  const [coleccionGuardada, setColeccionGuardada] = useState(false);
  const [activeIndex, setActiveIndex] = useState(0);
  const [mostrarRespuesta, setMostrarRespuesta] = useState(false);
  const [coleccionesExistentes, setColeccionesExistentes] = useState<ColeccionFlashcards[]>([]);
  const [coleccionSeleccionada, setColeccionSeleccionada] = useState<string>('nueva');
  const [cargandoColecciones, setCargandoColecciones] = useState(false);
  const [error, setError] = useState('');
  const [showRecommendations, setShowRecommendations] = useState(false);
  const [currentTaskId, setCurrentTaskId] = useState<string | null>(null);

  const { generateFlashcards, isGenerating, getActiveTask } = useBackgroundGeneration();
  const { getTask, removeTask } = useBackgroundTasks();
  const { executeWithGuard } = useFreeAccountGuard();
  const { user } = useAuth();
  const { plan: userPlan, isLoading: planLoading } = useUserPlan();
  const { refresh: refreshPlanLimits } = usePlanLimits();

  // Verificar si hay una tarea activa de flashcards
  const activeTask = getActiveTask('flashcards');
  const isLoading = isGenerating('flashcards');

  // Suscribirse a los resultados de las tareas de flashcards
  const { resetProcessed, getLatestCompletedTask, isInitialized } = useTaskResults({
    taskType: 'flashcards',
    onResult: (result, taskId) => {
      setFlashcardsGeneradas(result);
      setCurrentTaskId(taskId);
      toast.success('¡Flashcards generadas exitosamente!');
      refreshPlanLimits(); // Refrescar límites después de generación exitosa
    },
    onError: (error, taskId) => {
      toast.error(`Error al generar flashcards: ${error}`);
      setCurrentTaskId(taskId);
    }
  });

  // Verificar si hay resultados de flashcards al montar el componente
  useEffect(() => {
    if (!isInitialized) return;

    // Solo verificar si no hay flashcards generadas actualmente
    if (flashcardsGeneradas.length === 0) {
      const latestTask = getLatestCompletedTask();
      if (latestTask && latestTask.result) {
        console.log('🔄 Aplicando resultado de flashcards recuperado automáticamente');
        setFlashcardsGeneradas(latestTask.result);

        // Establecer título si no existe
        if (!tituloColeccion && latestTask.title) {
          setTituloColeccion(`Flashcards recuperadas: ${latestTask.title}`);
        }

        toast.success('Flashcards recuperadas automáticamente', {
          duration: 3000,
          icon: '🔄'
        });
      }
    }
  }, [isInitialized, flashcardsGeneradas.length, getLatestCompletedTask, tituloColeccion]);

  const {
    register,
    handleSubmit: handleSubmitForm,
    formState: { errors }
  } = useForm({
    resolver: zodResolver(flashcardFormSchema),
    defaultValues: { peticion: '', cantidad: 10 }
  });

  const cargarColecciones = useCallback(async () => {
    setCargandoColecciones(true);
    try {
      const colecciones = await obtenerColeccionesFlashcards();
      setColeccionesExistentes(colecciones);
    } catch (error) {
      console.error('Error al cargar colecciones:', error);
      toast.error('No se pudieron cargar las colecciones existentes.');
    } finally {
      setCargandoColecciones(false);
    }
  }, []);

  // Cargar colecciones existentes al montar el componente
  useEffect(() => {
    cargarColecciones();
  }, [cargarColecciones]);

  const onSubmit = async (data: { peticion: string; cantidad: number }) => {
    const contextos = documentosSeleccionados.map(doc => doc.contenido);
    setFlashcardsGeneradas([]);
    setColeccionGuardada(false);

    // Validar límites de plan gratuito
    const result = await executeWithGuard('flashcards', async () => {
      await generateFlashcards({
        peticion: data.peticion,
        contextos,
        cantidad: data.cantidad
      });

      // Establecer título si no existe
      if (!tituloColeccion) {
        setTituloColeccion(`Flashcards: ${data.peticion.substring(0, 50)}${data.peticion.length > 50 ? '...' : ''}`);
      }

      return true;
    }, data.cantidad);

    if (result.success) {
      // Mostrar mensaje informativo sobre la generación en segundo plano
      toast.success('Generación iniciada en segundo plano. Puedes continuar usando la aplicación.', {
        duration: 4000,
      });
    } else {
      toast.error(result.error || 'Error al iniciar la generación de flashcards');
    }
  };

  const handleGuardarColeccion = async () => {
    if (flashcardsGeneradas.length === 0) {
      setError('No hay flashcards para guardar');
      return;
    }

    // Si se seleccionó crear una nueva colección, validar el título
    if (coleccionSeleccionada === 'nueva' && !tituloColeccion.trim()) {
      setError('Por favor, proporciona un título para la nueva colección');
      return;
    }

    // Si se seleccionó una colección existente, validar que se haya seleccionado una
    if (coleccionSeleccionada !== 'nueva' && coleccionSeleccionada === '') {
      setError('Por favor, selecciona una colección existente');
      return;
    }

    setError('');

    try {
      let coleccionId: string | null;

      // Si es una nueva colección, crearla
      if (coleccionSeleccionada === 'nueva') {
        coleccionId = await crearColeccionFlashcards(tituloColeccion, descripcionColeccion);

        if (!coleccionId) {
          throw new Error('No se pudo crear la colección');
        }
      } else {
        // Usar la colección existente seleccionada
        coleccionId = coleccionSeleccionada;
      }

      // Preparar las flashcards para guardar
      const flashcardsParaGuardar = flashcardsGeneradas.map(fc => ({
        coleccion_id: coleccionId as string,
        pregunta: fc.pregunta,
        respuesta: fc.respuesta
      }));

      // Guardar las flashcards
      const resultado = await guardarFlashcards(flashcardsParaGuardar);

      if (!resultado) {
        throw new Error('No se pudieron guardar las flashcards');
      }

      setColeccionGuardada(true);

      // Recargar las colecciones para tener la lista actualizada
      if (coleccionSeleccionada === 'nueva') {
        await cargarColecciones();
      }

      // Limpiar automáticamente después de guardar exitosamente
      setFlashcardsGeneradas([]);
      setColeccionGuardada(false);
      setTituloColeccion('');
      setDescripcionColeccion('');
      setColeccionSeleccionada('nueva');
      setActiveIndex(0);
      setMostrarRespuesta(false);

      // Limpiar la tarea completada específica
      if (currentTaskId) {
        removeTask(currentTaskId);
        setCurrentTaskId(null);
      }

      toast.success('Flashcards guardadas correctamente. Puedes generar unas nuevas.');
    } catch (error) {
      console.error('Error al guardar las flashcards:', error);
      setError('Ha ocurrido un error al guardar las flashcards. Por favor, inténtalo de nuevo.');
    }
  };

  const handleNextCard = () => {
    if (activeIndex < flashcardsGeneradas.length - 1) {
      setActiveIndex(activeIndex + 1);
      setMostrarRespuesta(false);
    }
  };

  const handlePrevCard = () => {
    if (activeIndex > 0) {
      setActiveIndex(activeIndex - 1);
      setMostrarRespuesta(false);
    }
  };

  const toggleRespuesta = () => {
    setMostrarRespuesta(!mostrarRespuesta);
  };

  return (
    <div className="mt-8 border-t pt-8">
      <h2 className="text-xl font-bold mb-4">Generador de Flashcards</h2>

      {/* Información de límites según el plan del usuario */}
      {!planLoading && userPlan === 'free' && (
        <div className="mb-6 p-4 bg-orange-50 border border-orange-200 rounded-lg">
          <div className="flex items-start space-x-3">
            <FiLock className="w-5 h-5 text-orange-600 mt-0.5" />
            <div>
              <h3 className="font-medium text-orange-900">Límites del Plan Gratuito</h3>
              <p className="text-sm text-orange-700 mt-1">
                Máximo {PLAN_CONFIGURATIONS.free.limits.flashcardsForTrial} flashcards durante el período de prueba. Para generar flashcards ilimitadas,
                <Link href="/upgrade-plan" className="font-medium underline hover:text-orange-800 ml-1">
                  actualiza tu plan
                </Link>.
              </p>
            </div>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmitForm(onSubmit)} className="space-y-4">
        <div>
          <div className="flex items-center justify-between mb-2">
            <label htmlFor="peticion" className="text-gray-700 text-sm font-bold">
              Describe las flashcards que deseas generar:
            </label>
            <button
              type="button"
              onClick={() => setShowRecommendations(true)}
              className="text-blue-600 hover:text-blue-800 text-sm font-medium underline"
            >
              💡 Ver recomendaciones
            </button>
          </div>
          <textarea
            id="peticion"
            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            rows={3}
            {...register('peticion')}
            placeholder="Ej: Genera flashcards sobre los conceptos principales del tema 1"
            disabled={isLoading}
          />
          {errors.peticion && <span className="text-red-500 text-sm">{errors.peticion.message}</span>}
          <p className="text-sm text-gray-500 mt-1">
            La IA generará flashcards basadas en los documentos seleccionados y tu petición.
          </p>
        </div>

        <div>
          <label htmlFor="cantidad" className="block text-gray-700 text-sm font-bold mb-2">
            Número de flashcards:
          </label>
          <input
            id="cantidad"
            type="number"
            min="1"
            max="30"
            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            {...register('cantidad', { valueAsNumber: true })}
            disabled={isLoading}
          />
          {errors.cantidad && <span className="text-red-500 text-sm">{errors.cantidad.message}</span>}
          <p className="text-sm text-gray-500 mt-1">
            Especifica cuántas flashcards quieres generar (entre 1 y 30).
          </p>
        </div>

        {error && (
          <div className="text-red-500 text-sm">{error}</div>
        )}

        <div>
          <button
            type="submit"
            className="bg-orange-500 hover:bg-orange-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
            disabled={isLoading || documentosSeleccionados.length === 0}
          >
            {isLoading ? 'Generando...' : 'Generar Flashcards'}
          </button>
        </div>
      </form>

      {isLoading && (
        <div className="mt-4 text-center">
          <p className="text-gray-600">Generando flashcards, por favor espera...</p>
          <div className="mt-2 flex justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
          </div>
        </div>
      )}

      {flashcardsGeneradas.length > 0 && (
        <div className="mt-6">
          <h3 className="text-lg font-semibold mb-4">Flashcards generadas ({flashcardsGeneradas.length})</h3>

          {/* Formulario para guardar la colección */}
          {!coleccionGuardada && (
            <div className="bg-gray-100 p-4 rounded-lg mb-6">
              <h4 className="font-medium mb-2">Guardar flashcards</h4>

              {/* Selector de colección */}
              <div className="mb-4">
                <label htmlFor="tipoColeccion" className="block text-sm font-medium text-gray-700 mb-1">
                  ¿Dónde quieres guardar estas flashcards?
                </label>
                <div className="flex flex-col space-y-2">
                  <div className="flex items-center">
                    <input
                      type="radio"
                      id="nuevaColeccion"
                      name="tipoColeccion"
                      value="nueva"
                      checked={coleccionSeleccionada === 'nueva'}
                      onChange={() => setColeccionSeleccionada('nueva')}
                      className="mr-2"
                      disabled={isLoading}
                    />
                    <label htmlFor="nuevaColeccion" className="text-sm text-gray-700">
                      Crear nueva colección
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="radio"
                      id="coleccionExistente"
                      name="tipoColeccion"
                      value="existente"
                      checked={coleccionSeleccionada !== 'nueva'}
                      onChange={() => {
                        // Seleccionar la primera colección por defecto si hay alguna
                        if (coleccionesExistentes.length > 0) {
                          setColeccionSeleccionada(coleccionesExistentes[0].id);
                        } else {
                          setColeccionSeleccionada('');
                        }
                      }}
                      className="mr-2"
                      disabled={isLoading || coleccionesExistentes.length === 0}
                    />
                    <label htmlFor="coleccionExistente" className="text-sm text-gray-700">
                      Añadir a una colección existente
                      {coleccionesExistentes.length === 0 && (
                        <span className="text-gray-500 ml-2">(No hay colecciones disponibles)</span>
                      )}
                    </label>
                  </div>
                </div>
              </div>

              {/* Formulario para nueva colección */}
              {coleccionSeleccionada === 'nueva' && (
                <div className="space-y-3">
                  <div>
                    <label htmlFor="tituloColeccion" className="block text-sm font-medium text-gray-700 mb-1">
                      Título de la nueva colección:
                    </label>
                    <input
                      type="text"
                      id="tituloColeccion"
                      className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                      value={tituloColeccion}
                      onChange={(e) => setTituloColeccion(e.target.value)}
                      disabled={isLoading}
                    />
                  </div>
                  <div>
                    <label htmlFor="descripcionColeccion" className="block text-sm font-medium text-gray-700 mb-1">
                      Descripción (opcional):
                    </label>
                    <textarea
                      id="descripcionColeccion"
                      className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                      rows={2}
                      value={descripcionColeccion}
                      onChange={(e) => setDescripcionColeccion(e.target.value)}
                      disabled={isLoading}
                    />
                  </div>
                </div>
              )}

              {/* Selector de colección existente */}
              {coleccionSeleccionada !== 'nueva' && coleccionesExistentes.length > 0 && (
                <div>
                  <label htmlFor="coleccionExistenteSelect" className="block text-sm font-medium text-gray-700 mb-1">
                    Selecciona una colección:
                  </label>
                  <select
                    id="coleccionExistenteSelect"
                    className="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    value={coleccionSeleccionada}
                    onChange={(e) => setColeccionSeleccionada(e.target.value)}
                    disabled={isLoading}
                  >
                    {coleccionesExistentes.map(coleccion => (
                      <option key={coleccion.id} value={coleccion.id}>
                        {coleccion.titulo}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              {/* Botón de guardar */}
              <div className="mt-4">
                <button
                  type="button"
                  onClick={handleGuardarColeccion}
                  className="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                  disabled={false}
                >
                  Guardar flashcards
                </button>
              </div>
            </div>
          )}

          {coleccionGuardada && (
            <div className="bg-green-100 text-green-800 p-4 rounded-lg mb-6">
              <p className="font-medium">
                {coleccionSeleccionada === 'nueva'
                  ? '¡Nueva colección creada correctamente!'
                  : '¡Flashcards añadidas a la colección correctamente!'}
              </p>
              <p className="text-sm mt-1">Puedes acceder a {coleccionSeleccionada === 'nueva' ? 'ella' : 'las flashcards'} desde la sección de "Mis Flashcards".</p>
            </div>
          )}

          {/* Visor de flashcards */}
          <div className="bg-white border rounded-lg shadow-md p-6 mb-4">
            <div className="flex justify-between items-center mb-4">
              <button
                onClick={handlePrevCard}
                disabled={activeIndex === 0}
                className={`p-2 rounded-full ${
                  activeIndex === 0 ? 'text-gray-400 cursor-not-allowed' : 'text-gray-700 hover:bg-gray-200'
                }`}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              <span className="text-gray-600">
                {activeIndex + 1} de {flashcardsGeneradas.length}
              </span>
              <button
                onClick={handleNextCard}
                disabled={activeIndex === flashcardsGeneradas.length - 1}
                className={`p-2 rounded-full ${
                  activeIndex === flashcardsGeneradas.length - 1 ? 'text-gray-400 cursor-not-allowed' : 'text-gray-700 hover:bg-gray-200'
                }`}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </div>

            <div
              className="min-h-[200px] flex items-center justify-center cursor-pointer"
              onClick={toggleRespuesta}
            >
              <div className="text-center p-4 w-full">
                {!mostrarRespuesta ? (
                  <div className="font-semibold text-lg">{flashcardsGeneradas[activeIndex].pregunta}</div>
                ) : (
                  <div>
                    <div className="font-semibold text-lg mb-2">{flashcardsGeneradas[activeIndex].pregunta}</div>
                    <div className="border-t pt-4 text-left whitespace-pre-wrap">{flashcardsGeneradas[activeIndex].respuesta}</div>
                  </div>
                )}
              </div>
            </div>

            <div className="mt-4 text-center">
              <button
                onClick={toggleRespuesta}
                className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
              >
                {mostrarRespuesta ? 'Ocultar respuesta' : 'Mostrar respuesta'}
              </button>
            </div>
          </div>

          {/* Lista de todas las flashcards */}
          <div className="mt-6">
            <h4 className="font-medium mb-2">Todas las flashcards:</h4>
            <div className="space-y-2">
              {flashcardsGeneradas.map((card, index) => (
                <div
                  key={index}
                  className={`p-3 border rounded-lg cursor-pointer hover:bg-gray-50 ${
                    index === activeIndex ? 'border-blue-500 bg-blue-50' : ''
                  }`}
                  onClick={() => {
                    setActiveIndex(index);
                    setMostrarRespuesta(false);
                  }}
                >
                  <p className="font-medium">{card.pregunta}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Modal de recomendaciones */}
      <FlashcardRecommendationsModal
        isOpen={showRecommendations}
        onClose={() => setShowRecommendations(false)}
      />
    </div>
  );
}
