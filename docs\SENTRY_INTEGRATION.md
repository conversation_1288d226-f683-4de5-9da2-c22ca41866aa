# Integración de Sentry - OposiAI

## 📋 Resumen

Este documento describe la implementación completa de Sentry para monitoreo de errores en tiempo real en la plataforma OposiAI.

## 🚀 Estado de la Implementación

✅ **COMPLETADO** - Todas las fases implementadas exitosamente

### Fases Completadas:

1. **✅ Configuración Inicial de Sentry**
   - SDK @sentry/nextjs instalado
   - Wizard de configuración ejecutado
   - Variables de entorno configuradas

2. **✅ Instrumentación de Endpoints Críticos**
   - Webhook de Stripe (`src/app/api/stripe/webhook/route.ts`)
   - Cliente OpenAI (`src/lib/openai/openaiClient.ts`)
   - API principal de IA (`src/app/api/ai/route.ts`)
   - Endpoints de autenticación

3. **✅ Instrumentación de Endpoints de Administración**
   - Limpieza de cuentas gratuitas expiradas
   - Procesamiento de períodos de gracia
   - Envío de recordatorios

4. **✅ Verificación y Despliegue**
   - Endpoint de prueba creado
   - Configuración de producción verificada

## 🔧 Configuración

### Variables de Entorno

```bash
# .env.local
NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/4509597830152272
```

### Archivos de Configuración

- `sentry.server.config.ts` - Configuración del servidor
- `sentry.edge.config.ts` - Configuración para edge runtime
- `next.config.js` - Integración con Next.js y build plugin

## 📊 Monitoreo Implementado

### Endpoints Instrumentados

| Endpoint | Sección | Descripción |
|----------|---------|-------------|
| `/api/stripe/webhook` | `stripe-webhook` | Procesamiento de webhooks de Stripe |
| `/lib/openai/openaiClient.ts` | `openai-client` | Llamadas a la API de OpenAI |
| `/api/ai` | `ai-api` | Endpoint principal de IA |
| `/api/auth/register-free` | `auth-register-free` | Registro de usuarios gratuitos |
| `/api/auth/pre-register-paid` | `auth-pre-register-paid` | Pre-registro de usuarios de pago |
| `/api/auth/confirm-plan-upgrade` | `auth-confirm-plan-upgrade` | Confirmación de upgrades |
| `/api/admin/cleanup-expired-free` | `admin-cleanup-expired-free` | Limpieza de cuentas expiradas |
| `/api/admin/process-expired-grace-periods` | `admin-process-expired-grace-periods` | Procesamiento de períodos de gracia |
| `/api/admin/send-grace-period-reminders` | `admin-send-grace-period-reminders` | Envío de recordatorios |

### Patrón de Instrumentación

```typescript
import * as Sentry from "@sentry/nextjs";

try {
  // Código de la función
} catch (error) {
  Sentry.captureException(error, {
    tags: { section: "nombre-seccion" },
    extra: {
      context: "Descripción del contexto del error",
      // datos adicionales relevantes
      timestamp: new Date().toISOString()
    },
  });
  
  // Manejo del error existente
}
```

## 🧪 Testing

### Endpoint de Prueba

URL: `/api/test-sentry`

#### Tipos de Prueba Disponibles:

1. **Información**: `GET /api/test-sentry?type=info`
2. **Advertencia**: `GET /api/test-sentry?type=warning`
3. **Error**: `GET /api/test-sentry?type=error`
4. **Excepción con contexto**: `GET /api/test-sentry?type=exception`
5. **Breadcrumbs**: `GET /api/test-sentry?type=breadcrumbs`

### Página de Ejemplo

URL: `/sentry-example-page` (creada automáticamente por el wizard)

## 📈 Dashboard de Sentry

### Información del Proyecto

- **Organización**: oposiai
- **Proyecto**: javascript-nextjs
- **URL**: https://oposiai.sentry.io/

### Características Habilitadas

- ✅ **Error Tracking** - Captura automática de errores
- ✅ **Performance Monitoring** - Monitoreo de rendimiento
- ✅ **Session Replay** - Reproducción de sesiones de usuario
- ✅ **Source Maps** - Mapeo de código fuente para debugging
- ✅ **Release Tracking** - Seguimiento de versiones

## 🔍 Contexto de Errores

Cada error capturado incluye:

### Tags Estándar
- `section`: Identifica la sección/módulo donde ocurrió el error

### Información Extra
- `context`: Descripción del contexto del error
- `timestamp`: Marca de tiempo del error
- Datos específicos del endpoint (headers, parámetros, etc.)

### Ejemplos de Contexto Rico

```typescript
// Webhook de Stripe
Sentry.captureException(error, {
  tags: { section: "stripe-webhook" },
  extra: {
    context: "Critical error processing Stripe webhook.",
    request_headers: Object.fromEntries(request.headers),
    processingTime
  },
});

// Cliente OpenAI
Sentry.captureException(error, {
  tags: { section: "openai-client" },
  extra: {
    context: "Error calling OpenAI API.",
    model,
    errorCode: error.code,
    errorType: error.constructor?.name,
    timestamp: new Date().toISOString()
  },
});
```

## 🚀 Despliegue

### Configuración de Producción

1. **Variables de Entorno**: Asegurar que `NEXT_PUBLIC_SENTRY_DSN` esté configurada
2. **Source Maps**: Se suben automáticamente durante el build
3. **Releases**: Se crean automáticamente con cada despliegue

### Verificación Post-Despliegue

1. Verificar que los errores aparecen en el dashboard de Sentry
2. Probar el endpoint `/api/test-sentry` en producción
3. Confirmar que las source maps están funcionando

## 📚 Recursos Adicionales

- [Documentación de Sentry para Next.js](https://docs.sentry.io/platforms/javascript/guides/nextjs/)
- [Dashboard del Proyecto](https://oposiai.sentry.io/)
- [Configuración de Alertas](https://oposiai.sentry.io/settings/projects/javascript-nextjs/alerts/)

## 🔧 Mantenimiento

### Tareas Regulares

1. **Revisar errores semanalmente** en el dashboard
2. **Configurar alertas** para errores críticos
3. **Limpiar errores resueltos** periódicamente
4. **Actualizar SDK** cuando haya nuevas versiones

### Contacto

Para soporte técnico relacionado con Sentry, contactar al equipo de desarrollo.
