import { supabase } from '@/lib/supabase/client';
import { obtenerFlashcardsPorColeccionId } from '@/lib/supabase/flashcardsService';

// Tipos para las estadísticas

interface RevisionHistorial {
  id: string;
  flashcard_id: string;
  dificultad: 'facil' | 'normal' | 'dificil';
  fecha: string;
}

/**
 * Obtiene estadísticas de una colección de flashcards
 */
export async function obtenerEstadisticasColeccion(coleccionId: string) {
  // Obtener todas las flashcards de la colección
  const flashcards = await obtenerFlashcardsPorColeccionId(coleccionId);

  if (flashcards.length === 0) {
    return {
      total: 0,
      nuevas: 0,
      aprendiendo: 0,
      repasando: 0,
      aprendidas: 0,
      paraHoy: 0,
    };
  }

  // Obtener el progreso de todas las flashcards en una sola consulta
  const { data: progresos, error } = await supabase
    .from('progreso_flashcards')
    .select('*')
    .in('flashcard_id', flashcards.map(f => f.id));

  if (error) {
    console.error('Error al obtener progreso de flashcards:', error);
    return {
      total: flashcards.length,
      nuevas: flashcards.length,
      aprendiendo: 0,
      repasando: 0,
      aprendidas: 0,
      paraHoy: flashcards.length,
    };
  }

  // Inicializar contadores
  const estadisticas = {
    total: flashcards.length,
    nuevas: 0,
    aprendiendo: 0,
    repasando: 0,
    aprendidas: 0,
    paraHoy: 0,
  };

  // Fecha actual para comparar
  const ahora = new Date();

  // Contar flashcards por estado
  flashcards.forEach(flashcard => {
    const progreso = progresos?.find(p => p.flashcard_id === flashcard.id);

    if (!progreso) {
      // Si no hay progreso, es una tarjeta nueva
      estadisticas.nuevas++;
      estadisticas.paraHoy++; // Las nuevas siempre se estudian hoy
    } else {
      // Contar por estado
      switch (progreso.estado) {
        case 'nuevo':
          estadisticas.nuevas++;
          break;
        case 'aprendiendo':
          estadisticas.aprendiendo++;
          break;
        case 'repasando':
          estadisticas.repasando++;
          break;
        case 'aprendido':
          estadisticas.aprendidas++;
          break;
      }

      // Contar las que deben ser estudiadas hoy
      const proximaRevision = new Date(progreso.proxima_revision);
      // Eliminar la hora para comparar solo la fecha
      const proximaRevisionSinHora = new Date(
        proximaRevision.getFullYear(),
        proximaRevision.getMonth(),
        proximaRevision.getDate()
      );
      const hoy = new Date(
        ahora.getFullYear(),
        ahora.getMonth(),
        ahora.getDate()
      );

      if (proximaRevisionSinHora <= hoy) {
        estadisticas.paraHoy++;
      }
    }
  });

  return estadisticas;
}

/**
 * Obtiene estadísticas detalladas del estudio de flashcards
 */
export async function obtenerEstadisticasEstudio(coleccionId: string): Promise<any> {
  // Obtener todas las flashcards de la colección
  const flashcards = await obtenerFlashcardsPorColeccionId(coleccionId);

  if (flashcards.length === 0) {
    return {
      totalSesiones: 0,
      totalRevisiones: 0,
      distribucionDificultad: {
        dificil: 0,
        normal: 0,
        facil: 0,
      },
      progresoTiempo: [],
      tarjetasMasDificiles: [],
    };
  }

  // Obtener el historial de revisiones
  const { data: historial, error } = await supabase
    .from('historial_revisiones')
    .select('*')
    .in('flashcard_id', flashcards.map(f => f.id))
    .order('fecha', { ascending: true });

  if (error) {
    console.error('Error al obtener historial de revisiones:', error);
    return {
      totalSesiones: 0,
      totalRevisiones: 0,
      distribucionDificultad: {
        dificil: 0,
        normal: 0,
        facil: 0,
      },
      progresoTiempo: [],
      tarjetasMasDificiles: [],
    };
  }

  // Inicializar estadísticas
  const estadisticas: any = {
    totalSesiones: 0,
    totalRevisiones: historial?.length || 0,
    distribucionDificultad: {
      dificil: 0,
      normal: 0,
      facil: 0,
    },
    progresoTiempo: [],
    tarjetasMasDificiles: [],
  };

  // Si no hay historial, retornar estadísticas vacías
  if (!historial || historial.length === 0) {
    return estadisticas;
  }

  // Calcular distribución de dificultad
  historial.forEach(revision => {
    switch (revision.dificultad) {
      case 'dificil':
        estadisticas.distribucionDificultad.dificil++;
        break;
      case 'normal':
        estadisticas.distribucionDificultad.normal++;
        break;
      case 'facil':
        estadisticas.distribucionDificultad.facil++;
        break;
    }
  });

  // Calcular número de sesiones (días únicos)
  const fechasUnicas = new Set(historial.map(revision => {
    const fecha = new Date(revision.fecha);
    return `${fecha.getFullYear()}-${fecha.getMonth()}-${fecha.getDate()}`;
  }));
  estadisticas.totalSesiones = fechasUnicas.size;

  // Calcular tarjetas más difíciles
  const dificultadPorTarjeta = new Map<string, { dificil: number, normal: number, facil: number, total: number }>();

  historial.forEach(revision => {
    const actual = dificultadPorTarjeta.get(revision.flashcard_id) || { dificil: 0, normal: 0, facil: 0, total: 0 };

    switch (revision.dificultad) {
      case 'dificil':
        actual.dificil++;
        break;
      case 'normal':
        actual.normal++;
        break;
      case 'facil':
        actual.facil++;
        break;
    }

    actual.total++;
    dificultadPorTarjeta.set(revision.flashcard_id, actual);
  });

  // Convertir a array y ordenar por dificultad
  const tarjetasDificultad = Array.from(dificultadPorTarjeta.entries())
    .map(([id, stats]) => ({
      id,
      dificil: stats.dificil,
      normal: stats.normal,
      facil: stats.facil,
      totalRevisiones: stats.total,
      // Encontrar la pregunta correspondiente
      pregunta: flashcards.find(f => f.id === id)?.pregunta || 'Desconocida',
    }))
    .sort((a, b) => {
      // Ordenar por porcentaje de respuestas difíciles
      const pctDificilA = a.dificil / a.totalRevisiones;
      const pctDificilB = b.dificil / b.totalRevisiones;
      return pctDificilB - pctDificilA;
    })
    .slice(0, 5); // Tomar las 5 más difíciles

  estadisticas.tarjetasMasDificiles = tarjetasDificultad;

  return estadisticas;
}

/**
 * Obtiene el historial de revisiones de una flashcard
 */
export async function obtenerHistorialRevisiones(flashcardId: string): Promise<RevisionHistorial[]> {
  const { data, error } = await supabase
    .from('historial_revisiones')
    .select('*')
    .eq('flashcard_id', flashcardId)
    .order('fecha', { ascending: false });

  if (error) {
    console.error('Error al obtener historial de revisiones:', error);
    return [];
  }

  return data || [];
}

/**
 * Obtiene estadísticas detalladas de estudio para una colección
 */
export async function obtenerEstadisticasDetalladas(coleccionId: string): Promise<any> {
  try {
    // 1. Obtener todas las flashcards de la colección
    const flashcards = await obtenerFlashcardsPorColeccionId(coleccionId);

    if (flashcards.length === 0) {
      return {
        totalSesiones: 0,
        totalRevisiones: 0,
        distribucionDificultad: {
          dificil: 0,
          normal: 0,
          facil: 0,
        },
        progresoTiempo: [],
        tarjetasMasDificiles: []
      };
    }

    const flashcardIds = flashcards.map(f => f.id);

    // 2. Obtener todo el historial de revisiones para estas flashcards
    const { data: revisiones, error: revisionesError } = await supabase
      .from('historial_revisiones')
      .select('*')
      .in('flashcard_id', flashcardIds)
      .order('fecha', { ascending: true });

    if (revisionesError) {
      console.error('Error al obtener revisiones:', revisionesError);
      return null;
    }

    // 3. Obtener el progreso actual de las flashcards
    const { error: progresoError } = await supabase
      .from('progreso_flashcards')
      .select('*')
      .in('flashcard_id', flashcardIds);

    if (progresoError) {
      console.error('Error al obtener progreso:', progresoError);
      // Continuamos sin el progreso
    }

    // 4. Calcular estadísticas
    const estadisticas: any = {
      totalSesiones: 0,
      totalRevisiones: revisiones ? revisiones.length : 0,
      distribucionDificultad: {
        dificil: 0,
        normal: 0,
        facil: 0
      },
      progresoTiempo: [],
      tarjetasMasDificiles: []
    };

    // Calcular distribución de dificultad
    if (revisiones && revisiones.length > 0) {
      revisiones.forEach(rev => {
        if (rev.dificultad === 'dificil') estadisticas.distribucionDificultad.dificil++;
        else if (rev.dificultad === 'normal') estadisticas.distribucionDificultad.normal++;
        else if (rev.dificultad === 'facil') estadisticas.distribucionDificultad.facil++;
      });

      // Calcular sesiones (agrupando por día)
      const fechasSesiones = new Set<string>();
      revisiones.forEach(rev => {
        const fecha = new Date(rev.fecha).toISOString().split('T')[0];
        fechasSesiones.add(fecha);
      });
      estadisticas.totalSesiones = fechasSesiones.size;

      // Calcular tarjetas más difíciles
      const dificultadPorTarjeta = new Map<string, { dificil: number, normal: number, facil: number, total: number }>();

      flashcards.forEach(card => {
        dificultadPorTarjeta.set(card.id, { dificil: 0, normal: 0, facil: 0, total: 0 });
      });

      revisiones.forEach(rev => {
        const stats = dificultadPorTarjeta.get(rev.flashcard_id);
        if (stats) {
          if (rev.dificultad === 'dificil') stats.dificil++;
          else if (rev.dificultad === 'normal') stats.normal++;
          else if (rev.dificultad === 'facil') stats.facil++;
          stats.total++;
        }
      });

      // Convertir a array y ordenar por dificultad (más difíciles primero)
      estadisticas.tarjetasMasDificiles = flashcards
        .map(card => {
          const stats = dificultadPorTarjeta.get(card.id) || { dificil: 0, normal: 0, facil: 0, total: 0 };
          return {
            id: card.id,
            pregunta: card.pregunta,
            dificil: stats.dificil,
            normal: stats.normal,
            facil: stats.facil,
            totalRevisiones: stats.total
          };
        })
        .filter(card => card.totalRevisiones > 0) // Solo incluir tarjetas que se han revisado
        .sort((a, b) => {
          // Ordenar primero por ratio de dificultad (dificil / total)
          const ratioA = a.totalRevisiones > 0 ? a.dificil / a.totalRevisiones : 0;
          const ratioB = b.totalRevisiones > 0 ? b.dificil / b.totalRevisiones : 0;
          return ratioB - ratioA;
        })
        .slice(0, 10); // Limitar a las 10 más difíciles
    }

    return estadisticas;
  } catch (error) {
    console.error('Error al calcular estadísticas detalladas:', error);
    return null;
  }
}