/**
 * Utilidad para distribución proporcional de preguntas basada en puntuaciones de chunks
 */

export interface ChunkScore {
  chunkIndex: number;
  score: number;
  content: any; // El chunk original
}

export interface ProportionalDistribution {
  chunkIndex: number;
  score: number;
  percentage: number;
  targetQuestions: number;
  minQuestions: number;
  maxQuestions: number;
}

/**
 * Calcula la distribución proporcional de preguntas basada en puntuaciones
 */
export function calculateProportionalDistribution(
  chunkScores: ChunkScore[],
  totalQuestions: number,
  options: {
    minQuestionsPerChunk?: number;
    maxQuestionsPerChunk?: number;
    maxConcentrationPercentage?: number; // Máximo % que puede tener un chunk
  } = {}
): ProportionalDistribution[] {
  const {
    minQuestionsPerChunk = 3,
    maxQuestionsPerChunk = Math.ceil(totalQuestions * 0.6), // Máximo 60% del total
    maxConcentrationPercentage = 60 // Máximo 60% para un chunk
  } = options;

  // Calcular puntuación total
  const totalScore = chunkScores.reduce((sum, chunk) => sum + chunk.score, 0);
  
  if (totalScore === 0) {
    // Fallback: distribución uniforme si no hay puntuaciones
    const questionsPerChunk = Math.ceil(totalQuestions / chunkScores.length);
    return chunkScores.map((chunk, index) => ({
      chunkIndex: chunk.chunkIndex,
      score: chunk.score,
      percentage: 100 / chunkScores.length,
      targetQuestions: questionsPerChunk,
      minQuestions: minQuestionsPerChunk,
      maxQuestions: maxQuestionsPerChunk
    }));
  }

  // Calcular distribución proporcional inicial
  let distributions = chunkScores.map(chunk => {
    const percentage = (chunk.score / totalScore) * 100;
    const targetQuestions = Math.round((chunk.score / totalScore) * totalQuestions);
    
    return {
      chunkIndex: chunk.chunkIndex,
      score: chunk.score,
      percentage,
      targetQuestions,
      minQuestions: minQuestionsPerChunk,
      maxQuestions: maxQuestionsPerChunk
    };
  });

  // Aplicar límites de concentración máxima
  const maxQuestionsAllowed = Math.ceil(totalQuestions * (maxConcentrationPercentage / 100));
  distributions = distributions.map(dist => ({
    ...dist,
    targetQuestions: Math.min(dist.targetQuestions, maxQuestionsAllowed),
    maxQuestions: Math.min(dist.maxQuestions, maxQuestionsAllowed)
  }));

  // Aplicar límites mínimos y máximos
  distributions = distributions.map(dist => ({
    ...dist,
    targetQuestions: Math.max(
      minQuestionsPerChunk,
      Math.min(dist.targetQuestions, dist.maxQuestions)
    )
  }));

  // Ajustar para que la suma sea exactamente totalQuestions
  const currentTotal = distributions.reduce((sum, dist) => sum + dist.targetQuestions, 0);
  const difference = totalQuestions - currentTotal;

  if (difference !== 0) {
    // Redistribuir la diferencia proporcionalmente
    const sortedByScore = [...distributions].sort((a, b) => b.score - a.score);
    
    if (difference > 0) {
      // Necesitamos añadir preguntas - dar prioridad a chunks con mayor puntuación
      let remaining = difference;
      for (const dist of sortedByScore) {
        if (remaining <= 0) break;
        const canAdd = Math.min(remaining, dist.maxQuestions - dist.targetQuestions);
        if (canAdd > 0) {
          dist.targetQuestions += canAdd;
          remaining -= canAdd;
        }
      }
    } else {
      // Necesitamos quitar preguntas - quitar de chunks con menor puntuación
      let remaining = Math.abs(difference);
      for (const dist of sortedByScore.reverse()) {
        if (remaining <= 0) break;
        const canRemove = Math.min(remaining, dist.targetQuestions - dist.minQuestions);
        if (canRemove > 0) {
          dist.targetQuestions -= canRemove;
          remaining -= canRemove;
        }
      }
    }
  }

  // Recalcular porcentajes finales
  const finalTotal = distributions.reduce((sum, dist) => sum + dist.targetQuestions, 0);
  distributions = distributions.map(dist => ({
    ...dist,
    percentage: (dist.targetQuestions / finalTotal) * 100
  }));

  return distributions.sort((a, b) => a.chunkIndex - b.chunkIndex);
}

/**
 * Valida que la distribución sea correcta
 */
export function validateDistribution(
  distributions: ProportionalDistribution[],
  expectedTotal: number
): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // Verificar suma total
  const actualTotal = distributions.reduce((sum, dist) => sum + dist.targetQuestions, 0);
  if (actualTotal !== expectedTotal) {
    errors.push(`Total de preguntas no coincide: esperado ${expectedTotal}, actual ${actualTotal}`);
  }
  
  // Verificar límites mínimos
  distributions.forEach((dist, index) => {
    if (dist.targetQuestions < dist.minQuestions) {
      errors.push(`Chunk ${index} tiene menos preguntas que el mínimo: ${dist.targetQuestions} < ${dist.minQuestions}`);
    }
    if (dist.targetQuestions > dist.maxQuestions) {
      errors.push(`Chunk ${index} tiene más preguntas que el máximo: ${dist.targetQuestions} > ${dist.maxQuestions}`);
    }
  });
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Genera un resumen legible de la distribución
 */
export function generateDistributionSummary(distributions: ProportionalDistribution[]): string {
  const lines = [
    '📊 Distribución Proporcional de Preguntas:',
    '─'.repeat(50)
  ];
  
  distributions.forEach((dist, index) => {
    lines.push(
      `Chunk ${index + 1}: ${dist.targetQuestions} preguntas (${dist.percentage.toFixed(1)}%) - Score: ${dist.score}`
    );
  });
  
  const total = distributions.reduce((sum, dist) => sum + dist.targetQuestions, 0);
  lines.push('─'.repeat(50));
  lines.push(`Total: ${total} preguntas`);
  
  return lines.join('\n');
}

/**
 * Configuración por defecto para diferentes tipos de contenido
 */
export const PROPORTIONAL_DISTRIBUTION_CONFIGS = {
  tests: {
    minQuestionsPerChunk: 3,
    maxConcentrationPercentage: 60,
  },
  flashcards: {
    minQuestionsPerChunk: 2,
    maxConcentrationPercentage: 70, // Flashcards pueden concentrarse más
  },
  // Mapas mentales no usan distribución proporcional
} as const;
