// src/components/auth/PlanValidationWrapper.tsx
// Wrapper para validación de planes en componentes React

'use client';

import React, { useEffect, useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { createClient } from '@/lib/supabase/supabaseClient';
import { validateFeatureAccess } from '@/lib/auth/validateUserAccess';

interface PlanValidationWrapperProps {
  children: React.ReactNode;
  requiredFeature?: string;
  requiredPlan?: string[];
  tokensToUse?: number;
  fallbackComponent?: React.ReactNode;
  showUpgradePrompt?: boolean;
}

interface ValidationState {
  loading: boolean;
  hasAccess: boolean;
  reason?: string;
  userPlan?: string;
  upgradeRequired?: boolean;
  suggestedPlan?: string;
}

export default function PlanValidationWrapper({
  children,
  requiredFeature,
  requiredPlan,
  tokensToUse = 0,
  fallbackComponent,
  showUpgradePrompt = true
}: PlanValidationWrapperProps) {
  const router = useRouter();
  const [validation, setValidation] = useState<ValidationState>({
    loading: true,
    hasAccess: false
  });

  const validateAccess = useCallback(async () => {
    try {
      setValidation(prev => ({ ...prev, loading: true }));

      const supabase = createClient();

      // Verificar autenticación básica
      const { data: { user }, error: authError } = await supabase.auth.getUser();

      if (authError || !user) {
        router.push('/login');
        return;
      }

      // Si se requiere una característica específica, validarla
      if (requiredFeature) {
        const featureValidation = await validateFeatureAccess(requiredFeature, tokensToUse);

        if (!featureValidation.allowed) {
          setValidation({
            loading: false,
            hasAccess: false,
            reason: featureValidation.reason,
            upgradeRequired: featureValidation.upgradeRequired,
            suggestedPlan: featureValidation.suggestedPlan
          });
          return;
        }
      }

      // Si se requiere un plan específico, validarlo
      if (requiredPlan && requiredPlan.length > 0) {
        const { data: profile } = await supabase
          .from('user_profiles')
          .select('subscription_plan, payment_verified')
          .eq('user_id', user.id)
          .single();

        if (!profile) {
          router.push('/payment');
          return;
        }

        if (!requiredPlan.includes(profile.subscription_plan)) {
          setValidation({
            loading: false,
            hasAccess: false,
            reason: `Esta función requiere plan ${requiredPlan.join(' o ')}`,
            userPlan: profile.subscription_plan,
            upgradeRequired: true,
            suggestedPlan: requiredPlan[requiredPlan.length - 1] // Sugerir el plan más alto
          });
          return;
        }

        // Verificar pago para planes de pago
        if (profile.subscription_plan !== 'free' && !profile.payment_verified) {
          router.push('/payment?reason=payment_not_verified');
          return;
        }
      }

      // Si llegamos aquí, el usuario tiene acceso
      setValidation({
        loading: false,
        hasAccess: true
      });

    } catch (error) {
      console.error('Error validating access:', error);
      setValidation({
        loading: false,
        hasAccess: false,
        reason: 'Error de validación'
      });
    }
  }, [requiredFeature, requiredPlan, tokensToUse, router]);

  useEffect(() => {
    validateAccess();
  }, [validateAccess]);

  const handleUpgrade = () => {
    const upgradeUrl = validation.suggestedPlan 
      ? `/payment?upgrade=${validation.suggestedPlan}`
      : '/payment';
    router.push(upgradeUrl);
  };

  const handleGoBack = () => {
    router.back();
  };

  // Mostrar loading
  if (validation.loading) {
    return (
      <div className="flex items-center justify-center min-h-[200px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600">Verificando acceso...</span>
      </div>
    );
  }

  // Si no tiene acceso, mostrar componente de fallback o mensaje de upgrade
  if (!validation.hasAccess) {
    if (fallbackComponent) {
      return <>{fallbackComponent}</>;
    }

    if (showUpgradePrompt) {
      return (
        <div className="bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto">
          <div className="text-center">
            <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            
            <h3 className="text-lg font-semibold text-gray-800 mb-2">
              Acceso Restringido
            </h3>
            
            <p className="text-gray-600 mb-4">
              {validation.reason}
            </p>

            {validation.upgradeRequired && (
              <div className="bg-blue-50 rounded-lg p-4 mb-4">
                <p className="text-blue-800 text-sm">
                  {validation.userPlan && (
                    <>Tu plan actual: <strong>{getPlanDisplayName(validation.userPlan)}</strong><br /></>
                  )}
                  {validation.suggestedPlan && (
                    <>Plan recomendado: <strong>{getPlanDisplayName(validation.suggestedPlan)}</strong></>
                  )}
                </p>
              </div>
            )}

            <div className="space-y-3">
              {validation.upgradeRequired && (
                <button
                  onClick={handleUpgrade}
                  className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors font-medium"
                >
                  {validation.suggestedPlan 
                    ? `Actualizar a ${getPlanDisplayName(validation.suggestedPlan)}`
                    : 'Ver Planes'
                  }
                </button>
              )}
              
              <button
                onClick={handleGoBack}
                className="w-full bg-gray-200 text-gray-800 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors"
              >
                Volver
              </button>
            </div>
          </div>
        </div>
      );
    }

    // Si no se debe mostrar prompt de upgrade, no renderizar nada
    return null;
  }

  // Si tiene acceso, renderizar los children
  return <>{children}</>;
}

// Hook personalizado para usar la validación
export function usePlanValidation(requiredFeature?: string, tokensToUse: number = 0) {
  const [validation, setValidation] = useState<ValidationState>({
    loading: true,
    hasAccess: false
  });

  useEffect(() => {
    if (requiredFeature) {
      validateFeatureAccess(requiredFeature, tokensToUse).then(result => {
        setValidation({
          loading: false,
          hasAccess: result.allowed,
          reason: result.reason,
          upgradeRequired: result.upgradeRequired,
          suggestedPlan: result.suggestedPlan
        });
      });
    } else {
      setValidation({
        loading: false,
        hasAccess: true
      });
    }
  }, [requiredFeature, tokensToUse]);

  return validation;
}

// Componente de orden superior para proteger páginas completas
export function withPlanValidation<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  options: {
    requiredFeature?: string;
    requiredPlan?: string[];
    tokensToUse?: number;
  }
) {
  return function PlanValidatedComponent(props: P) {
    return (
      <PlanValidationWrapper
        requiredFeature={options.requiredFeature}
        requiredPlan={options.requiredPlan}
        tokensToUse={options.tokensToUse}
      >
        <WrappedComponent {...props} />
      </PlanValidationWrapper>
    );
  };
}

// Helper function para mostrar nombres de planes
function getPlanDisplayName(plan: string): string {
  const names: Record<string, string> = {
    'free': 'Gratis',
    'usuario': 'Usuario',
    'pro': 'Pro'
  };
  return names[plan] || plan;
}
