// src/utils/testTokenThresholds.ts
// ARCHIVO TEMPORAL SOLO PARA TESTING - ELIMINAR DESPUÉS DE VERIFICAR

import { toast } from 'react-hot-toast';

/**
 * Función temporal para testing del sistema de notificaciones de umbrales
 * SOLO PARA DESARROLLO - NO USAR EN PRODUCCIÓN
 */
export const testTokenThresholdNotifications = () => {
  console.log('🧪 Iniciando test de notificaciones de umbrales de tokens...');

  // Test 1: Umbral 50% (Warning)
  setTimeout(() => {
    console.log('📊 Simulando umbral 50%...');
    toast('Límite de Tokens: 50% alcanzado\nHas consumido el 50% de tus tokens. Considera revisar tu uso.', {
      duration: 10000,
      id: 'token-threshold-50',
      style: {
        background: '#f59e0b',
        color: '#fff',
      },
      icon: '⚠️'
    });
  }, 1000);

  // Test 2: Umbral 75% (Warning)
  setTimeout(() => {
    console.log('📊 Simulando umbral 75%...');
    toast('Límite de Tokens: 75% alcanzado\nHas consumido el 75% de tus tokens. Considera revisar tu uso.', {
      duration: 10000,
      id: 'token-threshold-75',
      style: {
        background: '#f59e0b',
        color: '#fff',
      },
      icon: '⚠️'
    });
  }, 3000);

  // Test 3: Umbral 90% (Error)
  setTimeout(() => {
    console.log('📊 Simulando umbral 90%...');
    toast.error('Límite de Tokens: 90% alcanzado\nHas consumido el 90% de tus tokens. Considera revisar tu uso.', {
      duration: 10000,
      id: 'token-threshold-90'
    });
  }, 5000);

  // Test 4: Umbral 100% (Error)
  setTimeout(() => {
    console.log('📊 Simulando umbral 100%...');
    toast.error('Límite de Tokens: 100% alcanzado\nHas consumido el 100% de tus tokens. Considera revisar tu uso.', {
      duration: 10000,
      id: 'token-threshold-100'
    });
  }, 7000);

  console.log('✅ Test programado. Las notificaciones aparecerán en los próximos 8 segundos.');
  console.log('🔍 Observa la esquina superior derecha para ver las notificaciones.');
};

/**
 * Función para simular un umbral específico
 */
export const testSpecificThreshold = (percentage: 50 | 75 | 90 | 100) => {
  console.log(`🧪 Simulando umbral ${percentage}%...`);
  
  if (percentage >= 90) {
    // Error notification
    toast.error(`Límite de Tokens: ${percentage}% alcanzado\nHas consumido el ${percentage}% de tus tokens. Considera revisar tu uso.`, {
      duration: 10000,
      id: `token-threshold-${percentage}`
    });
  } else {
    // Warning notification
    toast(`Límite de Tokens: ${percentage}% alcanzado\nHas consumido el ${percentage}% de tus tokens. Considera revisar tu uso.`, {
      duration: 10000,
      id: `token-threshold-${percentage}`,
      style: {
        background: '#f59e0b',
        color: '#fff',
      },
      icon: '⚠️'
    });
  }
};

// Hacer las funciones disponibles globalmente para testing
if (typeof window !== 'undefined') {
  (window as any).testTokenThresholds = testTokenThresholdNotifications;
  (window as any).testSpecificThreshold = testSpecificThreshold;
  
  console.log('🧪 Funciones de testing disponibles:');
  console.log('   - testTokenThresholds() - Prueba todos los umbrales');
  console.log('   - testSpecificThreshold(50|75|90|100) - Prueba un umbral específico');
}
