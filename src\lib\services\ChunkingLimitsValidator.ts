/**
 * Servicio de Validación de Límites para Chunking
 * 
 * Este servicio valida que el procesamiento por chunks no exceda los límites
 * de tokens, tiempo de procesamiento y recursos del sistema, considerando
 * el overhead real de múltiples llamadas a IA y metadatos.
 */

import { TokenEstimationService } from './tokenEstimationService';
import { type Documento } from '@/types/database';
import { type ChunkingConfig } from '@/lib/utils/textProcessing';
import { type DocumentChunkingResult } from '@/types/chunking';

/**
 * Configuración de límites para chunking
 */
export interface ChunkingLimitsConfig {
  /** Máximo número de chunks por documento */
  maxChunksPerDocument: number;
  /** Máximo número de tokens por chunk */
  maxTokensPerChunk: number;
  /** Máximo número de tokens totales por procesamiento */
  maxTotalTokens: number;
  /** Tiempo máximo de procesamiento en milisegundos */
  maxProcessingTimeMs: number;
  /** Overhead de tokens por chunk (metadatos, prompts, etc.) */
  tokenOverheadPerChunk: number;
  /** Factor de multiplicación para múltiples llamadas */
  multipleCallsOverheadFactor: number;
}

/**
 * Resultado de la validación de límites
 */
export interface ChunkingLimitsValidationResult {
  /** Indica si el procesamiento está dentro de los límites */
  isValid: boolean;
  /** Lista de violaciones encontradas */
  violations: ChunkingLimitViolation[];
  /** Estimaciones de recursos */
  estimations: {
    totalTokens: number;
    estimatedProcessingTime: number;
    totalChunks: number;
    tokenOverhead: number;
  };
  /** Recomendaciones para ajustar el procesamiento */
  recommendations: string[];
}

/**
 * Violación de límites de chunking
 */
export interface ChunkingLimitViolation {
  /** Tipo de violación */
  type: 'max_chunks' | 'max_tokens_per_chunk' | 'max_total_tokens' | 'max_processing_time';
  /** Valor actual que viola el límite */
  currentValue: number;
  /** Límite máximo permitido */
  maxAllowed: number;
  /** Descripción de la violación */
  description: string;
  /** Severidad de la violación */
  severity: 'low' | 'medium' | 'high' | 'critical';
}

/**
 * Configuración por defecto de límites de chunking
 */
export const DEFAULT_CHUNKING_LIMITS: ChunkingLimitsConfig = {
  maxChunksPerDocument: 50,           // Máximo 50 chunks por documento
  maxTokensPerChunk: 15000,           // Máximo 15k tokens por chunk
  maxTotalTokens: 200000,             // Máximo 200k tokens totales
  maxProcessingTimeMs: 300000,        // Máximo 5 minutos de procesamiento
  tokenOverheadPerChunk: 1000,        // 1k tokens de overhead por chunk
  multipleCallsOverheadFactor: 1.3    // 30% de overhead por múltiples llamadas
};

/**
 * Límites específicos por tipo de procesamiento
 * OPTIMIZADOS para reducir fragmentación excesiva
 */
export const PROCESSING_TYPE_LIMITS: Record<string, Partial<ChunkingLimitsConfig>> = {
  tests: {
    maxChunksPerDocument: 15,        // Reducido de 30 para evitar fragmentación
    maxTotalTokens: 150000,
    tokenOverheadPerChunk: 800
  },
  flashcards: {
    maxChunksPerDocument: 20,        // Reducido de 40 para mayor eficiencia
    maxTotalTokens: 120000,
    tokenOverheadPerChunk: 600
  },
  mindmaps: {
    maxChunksPerDocument: 15,        // Reducido de 20 para mejor coherencia
    maxTotalTokens: 100000,
    tokenOverheadPerChunk: 1200
  },
  summaries: {
    maxChunksPerDocument: 10,        // Reducido de 25 para resúmenes más cohesivos
    maxTotalTokens: 180000,
    tokenOverheadPerChunk: 1500
  }
};

/**
 * Servicio de Validación de Límites para Chunking
 */
export class ChunkingLimitsValidator {
  private static instance: ChunkingLimitsValidator;

  private constructor() {}

  /**
   * Singleton pattern
   */
  public static getInstance(): ChunkingLimitsValidator {
    if (!ChunkingLimitsValidator.instance) {
      ChunkingLimitsValidator.instance = new ChunkingLimitsValidator();
    }
    return ChunkingLimitsValidator.instance;
  }

  /**
   * Valida si un documento puede ser procesado con chunking dentro de los límites
   */
  validateDocumentForChunking(
    documento: Documento,
    processingType: string,
    chunkingConfig: ChunkingConfig,
    customLimits?: Partial<ChunkingLimitsConfig>
  ): ChunkingLimitsValidationResult {
    console.log(`🔍 Validando límites de chunking para documento: ${documento.titulo}`);
    
    // Obtener configuración de límites
    const limits = this.getEffectiveLimits(processingType, customLimits);
    
    // Estimar número de chunks
    const estimatedChunks = this.estimateChunkCount(documento, chunkingConfig);
    
    // Estimar tokens por chunk
    const tokensPerChunk = this.estimateTokensPerChunk(documento, estimatedChunks, chunkingConfig);
    
    // Calcular overhead total
    const tokenOverhead = this.calculateTokenOverhead(estimatedChunks, limits);
    
    // Estimar tokens totales
    const totalTokens = this.calculateTotalTokens(tokensPerChunk, estimatedChunks, tokenOverhead, limits);
    
    // Estimar tiempo de procesamiento
    const estimatedProcessingTime = this.estimateProcessingTime(estimatedChunks, tokensPerChunk);
    
    // Validar límites
    const violations = this.checkLimits({
      totalChunks: estimatedChunks,
      tokensPerChunk,
      totalTokens,
      estimatedProcessingTime
    }, limits);
    
    // Generar recomendaciones
    const recommendations = this.generateRecommendations(violations, {
      totalChunks: estimatedChunks,
      tokensPerChunk,
      totalTokens,
      estimatedProcessingTime
    }, limits);
    
    const result: ChunkingLimitsValidationResult = {
      isValid: violations.length === 0,
      violations,
      estimations: {
        totalTokens,
        estimatedProcessingTime,
        totalChunks: estimatedChunks,
        tokenOverhead
      },
      recommendations
    };
    
    if (result.isValid) {
      console.log(`✅ Documento válido para chunking: ${estimatedChunks} chunks, ~${totalTokens} tokens`);
    } else {
      console.warn(`⚠️ Documento excede límites de chunking: ${violations.length} violaciones`);
    }
    
    return result;
  }

  /**
   * Valida un resultado de chunking ya procesado
   */
  validateChunkingResult(
    chunkingResult: DocumentChunkingResult,
    processingType: string,
    customLimits?: Partial<ChunkingLimitsConfig>
  ): ChunkingLimitsValidationResult {
    const limits = this.getEffectiveLimits(processingType, customLimits);
    
    if (!chunkingResult.wasChunked || !Array.isArray(chunkingResult.content)) {
      return {
        isValid: true,
        violations: [],
        estimations: {
          totalTokens: 0,
          estimatedProcessingTime: 0,
          totalChunks: 1,
          tokenOverhead: 0
        },
        recommendations: []
      };
    }
    
    const chunks = chunkingResult.content;
    const totalChunks = chunks.length;
    
    // Estimar tokens por chunk promedio
    const avgChunkSize = chunks.reduce((sum, chunk) => sum + chunk.content.length, 0) / totalChunks;
    const tokensPerChunk = Math.ceil(avgChunkSize / 4); // Aproximación: 4 caracteres por token
    
    const tokenOverhead = this.calculateTokenOverhead(totalChunks, limits);
    const totalTokens = this.calculateTotalTokens(tokensPerChunk, totalChunks, tokenOverhead, limits);
    const estimatedProcessingTime = this.estimateProcessingTime(totalChunks, tokensPerChunk);
    
    const violations = this.checkLimits({
      totalChunks,
      tokensPerChunk,
      totalTokens,
      estimatedProcessingTime
    }, limits);
    
    const recommendations = this.generateRecommendations(violations, {
      totalChunks,
      tokensPerChunk,
      totalTokens,
      estimatedProcessingTime
    }, limits);
    
    return {
      isValid: violations.length === 0,
      violations,
      estimations: {
        totalTokens,
        estimatedProcessingTime,
        totalChunks,
        tokenOverhead
      },
      recommendations
    };
  }

  /**
   * Obtiene la configuración efectiva de límites
   */
  private getEffectiveLimits(
    processingType: string,
    customLimits?: Partial<ChunkingLimitsConfig>
  ): ChunkingLimitsConfig {
    const baseConfig = { ...DEFAULT_CHUNKING_LIMITS };
    const typeSpecificLimits = PROCESSING_TYPE_LIMITS[processingType] || {};
    
    return {
      ...baseConfig,
      ...typeSpecificLimits,
      ...customLimits
    };
  }

  /**
   * Estima el número de chunks que se generarán
   */
  private estimateChunkCount(documento: Documento, config: ChunkingConfig): number {
    const contentLength = documento.contenido.length;
    const effectiveChunkSize = config.maxChunkSize - config.overlapSize;
    return Math.ceil(contentLength / effectiveChunkSize);
  }

  /**
   * Estima tokens por chunk
   */
  private estimateTokensPerChunk(
    documento: Documento,
    estimatedChunks: number,
    config: ChunkingConfig
  ): number {
    const avgChunkSize = documento.contenido.length / estimatedChunks;
    return Math.ceil(avgChunkSize / 4); // Aproximación: 4 caracteres por token
  }

  /**
   * Calcula el overhead de tokens
   */
  private calculateTokenOverhead(chunks: number, limits: ChunkingLimitsConfig): number {
    return chunks * limits.tokenOverheadPerChunk;
  }

  /**
   * Calcula el total de tokens considerando overhead
   */
  private calculateTotalTokens(
    tokensPerChunk: number,
    chunks: number,
    overhead: number,
    limits: ChunkingLimitsConfig
  ): number {
    const baseTokens = tokensPerChunk * chunks;
    const totalWithOverhead = baseTokens + overhead;
    return Math.ceil(totalWithOverhead * limits.multipleCallsOverheadFactor);
  }

  /**
   * Estima el tiempo de procesamiento
   */
  private estimateProcessingTime(chunks: number, tokensPerChunk: number): number {
    // Estimación: ~2 segundos por chunk + tiempo base por token
    const baseTimePerChunk = 2000; // 2 segundos
    const timePerToken = 0.1; // 0.1ms por token
    return chunks * (baseTimePerChunk + tokensPerChunk * timePerToken);
  }

  /**
   * Verifica violaciones de límites
   */
  private checkLimits(
    metrics: {
      totalChunks: number;
      tokensPerChunk: number;
      totalTokens: number;
      estimatedProcessingTime: number;
    },
    limits: ChunkingLimitsConfig
  ): ChunkingLimitViolation[] {
    const violations: ChunkingLimitViolation[] = [];
    
    if (metrics.totalChunks > limits.maxChunksPerDocument) {
      violations.push({
        type: 'max_chunks',
        currentValue: metrics.totalChunks,
        maxAllowed: limits.maxChunksPerDocument,
        description: `El documento generaría ${metrics.totalChunks} chunks, excediendo el límite de ${limits.maxChunksPerDocument}`,
        severity: metrics.totalChunks > limits.maxChunksPerDocument * 1.5 ? 'critical' : 'high'
      });
    }
    
    if (metrics.tokensPerChunk > limits.maxTokensPerChunk) {
      violations.push({
        type: 'max_tokens_per_chunk',
        currentValue: metrics.tokensPerChunk,
        maxAllowed: limits.maxTokensPerChunk,
        description: `Los chunks estimados tendrían ${metrics.tokensPerChunk} tokens, excediendo el límite de ${limits.maxTokensPerChunk}`,
        severity: 'medium'
      });
    }
    
    if (metrics.totalTokens > limits.maxTotalTokens) {
      violations.push({
        type: 'max_total_tokens',
        currentValue: metrics.totalTokens,
        maxAllowed: limits.maxTotalTokens,
        description: `El procesamiento total requeriría ${metrics.totalTokens} tokens, excediendo el límite de ${limits.maxTotalTokens}`,
        severity: 'critical'
      });
    }
    
    if (metrics.estimatedProcessingTime > limits.maxProcessingTimeMs) {
      violations.push({
        type: 'max_processing_time',
        currentValue: metrics.estimatedProcessingTime,
        maxAllowed: limits.maxProcessingTimeMs,
        description: `El procesamiento tomaría aproximadamente ${Math.round(metrics.estimatedProcessingTime / 1000)} segundos, excediendo el límite de ${Math.round(limits.maxProcessingTimeMs / 1000)} segundos`,
        severity: 'high'
      });
    }
    
    return violations;
  }

  /**
   * Genera recomendaciones para resolver violaciones
   */
  private generateRecommendations(
    violations: ChunkingLimitViolation[],
    metrics: any,
    limits: ChunkingLimitsConfig
  ): string[] {
    const recommendations: string[] = [];
    
    if (violations.some(v => v.type === 'max_chunks')) {
      recommendations.push('Considera aumentar el tamaño máximo de chunk o reducir el solapamiento');
      recommendations.push('Divide el documento en secciones más pequeñas antes del procesamiento');
    }
    
    if (violations.some(v => v.type === 'max_total_tokens')) {
      recommendations.push('Reduce la cantidad de elementos a generar por chunk');
      recommendations.push('Considera procesar el documento en múltiples sesiones');
    }
    
    if (violations.some(v => v.type === 'max_processing_time')) {
      recommendations.push('Implementa procesamiento asíncrono con notificaciones de progreso');
      recommendations.push('Considera usar un modelo de IA más rápido para documentos grandes');
    }
    
    if (violations.length === 0) {
      recommendations.push('El documento está dentro de todos los límites y puede procesarse con chunking');
    }
    
    return recommendations;
  }
}

/**
 * Factory para crear instancias del validador
 */
export class ChunkingLimitsValidatorFactory {
  static create(): ChunkingLimitsValidator {
    return ChunkingLimitsValidator.getInstance();
  }
}

/**
 * Función de utilidad para validación rápida
 */
export function validateChunkingLimits(
  documento: Documento,
  processingType: string,
  chunkingConfig: ChunkingConfig,
  customLimits?: Partial<ChunkingLimitsConfig>
): ChunkingLimitsValidationResult {
  const validator = ChunkingLimitsValidatorFactory.create();
  return validator.validateDocumentForChunking(documento, processingType, chunkingConfig, customLimits);
}
