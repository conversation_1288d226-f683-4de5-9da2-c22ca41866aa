import React, { useState, useEffect } from 'react';
import { FiClock, FiUser } from 'react-icons/fi';

interface SessionIndicatorProps {
  isAuthenticated: boolean;
  onLogout?: () => void;
}

/**
 * Componente opcional que muestra un indicador de sesión activa
 * Se puede agregar al header para mostrar el estado de la sesión
 */
const SessionIndicator: React.FC<SessionIndicatorProps> = ({
  isAuthenticated,
  onLogout
}) => {
  const [sessionTime, setSessionTime] = useState(0);

  useEffect(() => {
    if (!isAuthenticated) {
      setSessionTime(0);
      return;
    }

    // Iniciar el contador de tiempo de sesión
    const startTime = Date.now();
    
    const interval = setInterval(() => {
      const elapsed = Math.floor((Date.now() - startTime) / 1000);
      setSessionTime(elapsed);
    }, 1000);

    return () => clearInterval(interval);
  }, [isAuthenticated]);

  const formatSessionTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="flex items-center space-x-2 text-sm text-gray-600">
      <div className="flex items-center space-x-1">
        <FiUser className="text-green-500" />
        <span className="text-green-600 font-medium">Conectado</span>
      </div>
      
      <div className="flex items-center space-x-1">
        <FiClock className="text-gray-400" />
        <span className="font-mono text-xs">
          {formatSessionTime(sessionTime)}
        </span>
      </div>

      {onLogout && (
        <button
          onClick={onLogout}
          className="text-xs text-gray-500 hover:text-red-600 transition-colors"
          title="Cerrar sesión"
        >
          Salir
        </button>
      )}
    </div>
  );
};

export default SessionIndicator;
