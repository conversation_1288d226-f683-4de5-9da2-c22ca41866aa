'use client';

import React from 'react';
import { useBackgroundTasks } from '@/contexts/BackgroundTasksContext';
import { BackgroundTask } from '@/contexts/BackgroundTasksContext';
import { 
  ClockIcon, 
  CheckCircleIcon, 
  ExclamationCircleIcon,
  ArrowPathIcon 
} from '@heroicons/react/24/outline';

interface TaskRecoveryIndicatorProps {
  taskType: BackgroundTask['type'];
  className?: string;
}

/**
 * Componente que muestra información sobre tareas recuperadas o en progreso
 * para un tipo específico de tarea
 */
export const TaskRecoveryIndicator: React.FC<TaskRecoveryIndicatorProps> = ({ 
  taskType, 
  className = '' 
}) => {
  const { tasks, isInitialized } = useBackgroundTasks();

  if (!isInitialized) {
    return null;
  }

  const relevantTasks = tasks.filter(task => task.type === taskType);
  const activeTasks = relevantTasks.filter(task => 
    task.status === 'pending' || task.status === 'processing'
  );
  const completedTasks = relevantTasks.filter(task => 
    task.status === 'completed'
  );
  const errorTasks = relevantTasks.filter(task => 
    task.status === 'error'
  );

  // No mostrar nada si no hay tareas relevantes
  if (relevantTasks.length === 0) {
    return null;
  }

  const getTaskTypeLabel = (type: BackgroundTask['type']): string => {
    const labels = {
      'test': 'Test',
      'flashcards': 'Flashcards',
      'mapa-mental': 'Mapa Mental',
      'plan-estudios': 'Plan de Estudios',
      'resumen': 'Resumen'
    };
    return labels[type] || type;
  };

  const formatTime = (date: Date): string => {
    return date.toLocaleTimeString('es-ES', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  return (
    <div className={`bg-blue-50 border border-blue-200 rounded-lg p-3 ${className}`}>
      <div className="flex items-center space-x-2 mb-2">
        <ArrowPathIcon className="h-4 w-4 text-blue-600" />
        <span className="text-sm font-medium text-blue-800">
          Estado de {getTaskTypeLabel(taskType)}
        </span>
      </div>

      <div className="space-y-1 text-xs">
        {activeTasks.length > 0 && (
          <div className="flex items-center space-x-2 text-amber-700">
            <ClockIcon className="h-3 w-3" />
            <span>
              {activeTasks.length} tarea{activeTasks.length > 1 ? 's' : ''} en progreso
            </span>
          </div>
        )}

        {completedTasks.length > 0 && (
          <div className="flex items-center space-x-2 text-green-700">
            <CheckCircleIcon className="h-3 w-3" />
            <span>
              Última completada: {formatTime(completedTasks[0].createdAt)}
            </span>
          </div>
        )}

        {errorTasks.length > 0 && (
          <div className="flex items-center space-x-2 text-red-700">
            <ExclamationCircleIcon className="h-3 w-3" />
            <span>
              {errorTasks.length} error{errorTasks.length > 1 ? 'es' : ''} reciente{errorTasks.length > 1 ? 's' : ''}
            </span>
          </div>
        )}
      </div>

      {completedTasks.length > 0 && (
        <div className="mt-2 pt-2 border-t border-blue-200">
          <p className="text-xs text-blue-600">
            💡 Los resultados se recuperan automáticamente al navegar entre páginas
          </p>
        </div>
      )}
    </div>
  );
};

export default TaskRecoveryIndicator;
