import { createServerSupabaseClient } from './server';
import { Documento } from './supabaseClient';

/**
 * Guarda un nuevo documento en la base de datos asociado al usuario actual (versión servidor)
 */
export async function guardarDocumentoServer(documento: Omit<Documento, 'id' | 'creado_en' | 'actualizado_en' | 'user_id'>): Promise<string | null> {
  try {
    // Crear cliente de Supabase para el servidor
    const supabase = await createServerSupabaseClient();
    
    // Obtener el usuario actual
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      console.error('No hay usuario autenticado:', userError?.message);
      return null;
    }

    // Añadir el user_id y tipo_original al documento
    const documentoConUsuario = {
      ...documento,
      user_id: user.id,
      tipo_original: documento.tipo_original // Asegurarse que tipo_original se pasa aquí
    };

    const { data, error } = await supabase
      .from('documentos')
      .insert([documentoConUsuario])
      .select();

    if (error) {
      console.error('Error al guardar documento:', error);
      return null;
    }

    return data?.[0]?.id || null;
  } catch (error) {
    console.error('Error al guardar documento:', error);
    return null;
  }
}
