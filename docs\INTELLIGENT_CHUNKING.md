# Sistema de Chunking Inteligente

## Descripción General

El sistema de chunking inteligente de OposiAI permite procesar documentos grandes de manera eficiente mediante la selección inteligente de chunks relevantes basada en las consultas del usuario. En lugar de procesar todos los chunks de un documento, el sistema identifica y procesa únicamente aquellos que son relevantes para la petición específica.

## Arquitectura

### Componentes Principales

1. **ChunkSelector** (`src/lib/utils/chunkSelector.ts`)
   - Función principal: `seleccionarChunksRelevantes`
   - Algoritmo de scoring basado en keywords y metadatos
   - Configuración flexible de parámetros

2. **Generadores Actualizados**
   - `testGenerator.ts`: Integra selección inteligente para tests
   - `flashcardGenerator.ts`: Integra selección inteligente para flashcards

3. **ChunkProcessingOrchestrator** (`src/lib/services/ChunkProcessingOrchestrator.ts`)
   - Coordinador del flujo de procesamiento
   - Arquitectura refactorizada para soportar selección inteligente

## Configuración

### Parámetros del ChunkSelector

```typescript
interface ChunkSelectorConfig {
  topN: number;                    // Número máximo de chunks a seleccionar (default: 3)
  relevanceThreshold: number;      // Umbral mínimo de relevancia (default: 0.3)
  sectionTitleWeight: number;      // Peso para coincidencias en títulos (default: 3.0)
  contentWeight: number;           // Peso para coincidencias en contenido (default: 1.0)
}
```

### Configuración Global

Los parámetros por defecto se pueden configurar en `src/config/chunking.ts`:

```typescript
export const CHUNK_SELECTOR_CONFIG = {
  TOP_N_CHUNKS: 3,
  RELEVANCE_THRESHOLD: 0.3,
  SECTION_TITLE_WEIGHT: 3.0,
  CONTENT_WEIGHT: 1.0
};
```

## Algoritmo de Selección

### 1. Extracción de Keywords
- Normalización de texto (minúsculas, eliminación de acentos)
- Filtrado de palabras vacías (stopwords)
- Tokenización por espacios y signos de puntuación

### 2. Scoring de Chunks
Para cada chunk se calcula un score basado en:

```
score = (coincidencias_titulo * sectionTitleWeight) + (coincidencias_contenido * contentWeight)
```

### 3. Selección Final
- Ordenamiento por score descendente
- Filtrado por umbral de relevancia
- Limitación al número máximo configurado (topN)

## Uso

### Ejemplo Básico

```typescript
import { seleccionarChunksRelevantes } from '@/lib/utils/chunkSelector';

const resultado = seleccionarChunksRelevantes(
  chunks,
  'matemáticas álgebra ecuaciones',
  {
    topN: 5,
    relevanceThreshold: 0.2
  }
);

console.log(`Seleccionados: ${resultado.selectedChunks.length} de ${resultado.totalChunks}`);
```

### Integración en Generadores

Los generadores de tests y flashcards ahora integran automáticamente la selección inteligente:

```typescript
// En testGenerator.ts
const selectionResult = seleccionarChunksRelevantes(chunks, instrucciones || '');
if (selectionResult.selectedChunks.length === 0) {
  throw new Error('No se pudo encontrar contenido relacionado con la consulta');
}
```

## Beneficios

### Rendimiento
- **Reducción de tokens**: Procesa solo chunks relevantes
- **Menor latencia**: Menos llamadas a APIs de IA
- **Mejor precisión**: Contenido más enfocado en la consulta

### Experiencia de Usuario
- **Respuestas más relevantes**: Contenido específico para la consulta
- **Menor tiempo de espera**: Procesamiento más rápido
- **Mejor calidad**: Tests y flashcards más precisos

## Testing

### Tests Unitarios
- `src/__tests__/chunking/chunkSelector.test.ts`
- Cobertura completa del algoritmo de selección
- Casos edge y configuraciones especiales

### Tests de Integración
- `src/__tests__/chunking/intelligentChunking.integration.test.ts`
- Verificación del flujo completo
- Tests de rendimiento

### Ejecutar Tests

```bash
# Tests unitarios del chunk selector
npm test -- src/__tests__/chunking/chunkSelector.test.ts

# Tests de integración
npm test -- src/__tests__/chunking/intelligentChunking.integration.test.ts

# Todos los tests de chunking
npm test -- src/__tests__/chunking/
```

## Monitoreo y Debugging

### Información de Selección

El resultado de `seleccionarChunksRelevantes` incluye información detallada:

```typescript
interface ChunkSelectionResult {
  selectedChunks: Chunk[];
  totalChunks: number;
  selectionCriteria: {
    keywords: string[];
    config: ChunkSelectorConfig;
  };
  scoringInfo: {
    averageScore: number;
    maxScore: number;
    minScore: number;
  };
}
```

### Logs de Desarrollo

En modo desarrollo, el sistema registra información sobre la selección:
- Keywords extraídas
- Scores calculados
- Chunks seleccionados vs descartados

## Migración y Compatibilidad

### Cambios en Interfaces

- `DocumentProcessingResult.content`: Cambió de `string | string[]` a `string | Chunk[]`
- Funciones de procesamiento: Ahora reciben `Chunk[]` en lugar de `string[]`

### Retrocompatibilidad

El sistema mantiene compatibilidad con documentos no chunkeados mediante el campo `wasChunked` en `DocumentProcessingResult`.

## Próximas Mejoras

1. **Machine Learning**: Implementar modelos de embeddings para mejor matching semántico
2. **Cache de Selecciones**: Cachear resultados de selección para consultas similares
3. **Métricas Avanzadas**: Tracking de efectividad de selecciones
4. **Configuración Dinámica**: Ajuste automático de parámetros basado en feedback
