/**
 * Selector de Chunks Relevantes
 * 
 * Este módulo implementa la lógica de selección inteligente de chunks
 * para optimizar el procesamiento de documentos largos, enfocando
 * la generación de IA solo en el contenido más relevante.
 */

import { type Chunk } from './textProcessing';
import { CHUNK_SELECTOR_CONFIG } from '@/config/chunking';

/**
 * Configuración del selector de chunks
 */
export interface ChunkSelectorConfig {
  /** Número máximo de chunks a seleccionar */
  topN: number;
  /** Puntuación mínima para considerar un chunk relevante */
  relevanceThreshold: number;
  /** Peso para coincidencias en títulos de sección (vs contenido) */
  sectionTitleWeight: number;
  /** Peso para coincidencias en contenido */
  contentWeight: number;
}

/**
 * Configuración por defecto del selector
 * Utiliza valores centralizados de la configuración global
 */
export const DEFAULT_CHUNK_SELECTOR_CONFIG: ChunkSelectorConfig = {
  topN: CHUNK_SELECTOR_CONFIG.TOP_N_CHUNKS,
  relevanceThreshold: CHUNK_SELECTOR_CONFIG.RELEVANCE_THRESHOLD,
  sectionTitleWeight: CHUNK_SELECTOR_CONFIG.SECTION_TITLE_WEIGHT,
  contentWeight: CHUNK_SELECTOR_CONFIG.CONTENT_WEIGHT
};

/**
 * Resultado de la selección de chunks con información de puntuación
 */
export interface ChunkSelectionResult {
  /** Chunks seleccionados ordenados por relevancia */
  selectedChunks: Chunk[];
  /** Número total de chunks evaluados */
  totalChunks: number;
  /** Criterios de selección utilizados */
  selectionCriteria: {
    keywords: string[];
    maxChunks: number;
    minScore: number;
  };
  /** Información de puntuación para debugging */
  scoringInfo: {
    chunkIndex: number;
    score: number;
    matchedKeywords: string[];
    sectionMatches: number;
    contentMatches: number;
  }[];
}

/**
 * Extrae palabras clave de una petición para buscar en los chunks.
 * Filtra palabras comunes y extrae números de apartados específicos.
 */
function extraerPalabrasClave(peticion: string): string[] {
  const preguntaLimpia = peticion.toLowerCase()
    .replace(/[^\w\sáéíóúñü.-]/g, ' ') // Conservamos puntos y guiones para números de apartado
    .replace(/\s+/g, ' ')
    .trim();

  // Palabras comunes en español que no aportan valor para la búsqueda
  const palabrasComunes = new Set([
    'el', 'la', 'los', 'las', 'un', 'una', 'unos', 'unas',
    'de', 'del', 'en', 'con', 'por', 'para', 'que', 'se',
    'es', 'son', 'está', 'están', 'como', 'qué', 'cuál',
    'genera', 'crea', 'haz', 'dime', 'sobre', 'acerca', 
    'test', 'flashcards', 'preguntas', 'respuestas',
    'apartado', 'tema', 'capítulo', 'sección'
  ]);

  // Extraer palabras significativas (3+ caracteres, no comunes)
  const palabras = preguntaLimpia.split(' ')
    .filter(palabra => palabra.length >= 3 && !palabrasComunes.has(palabra));

  // Extraer números de apartados (ej: 5.5, 1.2.3)
  const numerosApartados = preguntaLimpia.match(/\d+\.\d+(\.\d+)?/g) || [];

  // Combinar y eliminar duplicados
  return Array.from(new Set([...palabras, ...numerosApartados]));
}

/**
 * Calcula la puntuación de relevancia de un chunk basado en las palabras clave.
 */
function calcularPuntuacionChunk(
  chunk: Chunk, 
  palabrasClave: string[], 
  config: ChunkSelectorConfig
): {
  score: number;
  matchedKeywords: string[];
  sectionMatches: number;
  contentMatches: number;
} {
  let puntuacion = 0;
  const matchedKeywords: string[] = [];
  let sectionMatches = 0;
  let contentMatches = 0;

  const contenidoLower = chunk.content.toLowerCase();
  const seccionesLower = chunk.metadata.detectedSections.join(' ').toLowerCase();

  palabrasClave.forEach(palabra => {
    // Crear regex para buscar la palabra completa
    const regex = new RegExp(`\\b${palabra.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}`, 'gi');
    
    // Buscar coincidencias en títulos de sección (mayor peso)
    const coincidenciasSecciones = (seccionesLower.match(regex) || []).length;
    if (coincidenciasSecciones > 0) {
      puntuacion += coincidenciasSecciones * config.sectionTitleWeight;
      sectionMatches += coincidenciasSecciones;
      matchedKeywords.push(palabra);
    }

    // Buscar coincidencias en el contenido (menor peso)
    const coincidenciasContenido = (contenidoLower.match(regex) || []).length;
    if (coincidenciasContenido > 0) {
      puntuacion += coincidenciasContenido * config.contentWeight;
      contentMatches += coincidenciasContenido;
      if (!matchedKeywords.includes(palabra)) {
        matchedKeywords.push(palabra);
      }
    }
  });

  return {
    score: puntuacion,
    matchedKeywords,
    sectionMatches,
    contentMatches
  };
}

/**
 * Selecciona los chunks más relevantes basados en la petición del usuario.
 * Busca tanto en el contenido como en los metadatos de las secciones.
 * 
 * @param chunks Array de chunks a evaluar
 * @param peticion Petición del usuario con instrucciones específicas
 * @param config Configuración del selector (opcional)
 * @returns Resultado de la selección con chunks ordenados por relevancia
 */
export function seleccionarChunksRelevantes(
  chunks: Chunk[], 
  peticion: string, 
  config: ChunkSelectorConfig = DEFAULT_CHUNK_SELECTOR_CONFIG
): ChunkSelectionResult {
  // Si no hay petición, devolver array vacío
  if (!peticion || peticion.trim() === '') {
    console.log('📝 No hay petición específica, no se seleccionan chunks');
    return {
      selectedChunks: [],
      totalChunks: chunks.length,
      selectionCriteria: {
        keywords: [],
        maxChunks: config.topN,
        minScore: config.relevanceThreshold
      },
      scoringInfo: []
    };
  }

  const palabrasClave = extraerPalabrasClave(peticion);

  // Si no hay palabras clave útiles, devolver array vacío
  if (palabrasClave.length === 0) {
    console.log('📝 No se encontraron palabras clave útiles, no se seleccionan chunks');
    return {
      selectedChunks: [],
      totalChunks: chunks.length,
      selectionCriteria: {
        keywords: [],
        maxChunks: config.topN,
        minScore: config.relevanceThreshold
      },
      scoringInfo: []
    };
  }

  console.log(`🔍 Palabras clave extraídas: ${palabrasClave.join(', ')}`);

  // Calcular puntuación para cada chunk
  const chunksConPuntuacion = chunks.map((chunk, index) => {
    const scoring = calcularPuntuacionChunk(chunk, palabrasClave, config);
    return {
      chunk,
      chunkIndex: index,
      ...scoring
    };
  });

  // Filtrar chunks con puntuación mínima y ordenar por relevancia
  const chunksRelevantes = chunksConPuntuacion
    .filter(item => item.score >= config.relevanceThreshold)
    .sort((a, b) => b.score - a.score);

  // Si no se encuentra ninguno relevante, devolver array vacío
  // (el código que llama debe manejar este caso)
  if (chunksRelevantes.length === 0) {
    console.warn('⚠️ No se encontraron chunks relevantes para la petición');
    return {
      selectedChunks: [],
      totalChunks: chunks.length,
      selectionCriteria: {
        keywords: palabrasClave,
        maxChunks: config.topN,
        minScore: config.relevanceThreshold
      },
      scoringInfo: []
    };
  }

  // Seleccionar los top N chunks más relevantes
  const selectedItems = chunksRelevantes.slice(0, config.topN);

  console.log(`✅ Seleccionados ${selectedItems.length} chunks de ${chunks.length} total`);
  console.log(`📊 Puntuaciones: ${selectedItems.map(item => item.score.toFixed(2)).join(', ')}`);

  return {
    selectedChunks: selectedItems.map(item => item.chunk),
    totalChunks: chunks.length,
    selectionCriteria: {
      keywords: palabrasClave,
      maxChunks: config.topN,
      minScore: config.relevanceThreshold
    },
    scoringInfo: selectedItems.map(item => ({
      chunkIndex: item.chunkIndex,
      score: item.score,
      matchedKeywords: item.matchedKeywords,
      sectionMatches: item.sectionMatches,
      contentMatches: item.contentMatches
    }))
  };
}

/**
 * Función de utilidad para obtener información de debugging sobre la selección
 */
export function logChunkSelectionInfo(result: ChunkSelectionResult): void {
  console.log('🔍 Información de Selección de Chunks:');
  console.log(`   Palabras clave: ${result.selectionCriteria.keywords.join(', ')}`);
  console.log(`   Chunks evaluados: ${result.totalChunks}`);
  console.log(`   Chunks seleccionados: ${result.selectedChunks.length}`);

  result.scoringInfo.forEach((info, index) => {
    console.log(`   Chunk ${info.chunkIndex}: score=${info.score.toFixed(2)}, ` +
                `keywords=[${info.matchedKeywords.join(', ')}], ` +
                `sección=${info.sectionMatches}, contenido=${info.contentMatches}`);
  });
}
