// src/components/ui/UnauthorizedAccess.tsx
// Componente para mostrar mensajes de acceso no autorizado

'use client';

import React from 'react';
import { useRouter } from 'next/navigation';

interface UnauthorizedAccessProps {
  title?: string;
  message?: string;
  reason?: string;
  userPlan?: string;
  requiredPlan?: string[];
  feature?: string;
  showUpgradeButton?: boolean;
  showBackButton?: boolean;
  customActions?: React.ReactNode;
  variant?: 'card' | 'inline' | 'modal';
}

export default function UnauthorizedAccess({
  title = 'Acceso Restringido',
  message,
  reason,
  userPlan,
  requiredPlan = [],
  feature,
  showUpgradeButton = true,
  showBackButton = true,
  customActions,
  variant = 'card'
}: UnauthorizedAccessProps) {
  const router = useRouter();

  const getDefaultMessage = () => {
    if (message) return message;
    
    if (requiredPlan.length > 0) {
      const planNames = requiredPlan.map(getPlanDisplayName).join(' o ');
      return `Esta función requiere plan ${planNames}.`;
    }
    
    if (reason) return reason;
    
    return 'No tienes permisos para acceder a esta función.';
  };

  const getSuggestedPlan = () => {
    if (requiredPlan.length === 0) return null;
    
    // Si el usuario tiene plan gratuito, sugerir el plan más bajo requerido
    if (userPlan === 'free') {
      return requiredPlan[0];
    }
    
    // Si el usuario tiene plan usuario y se requiere pro, sugerir pro
    if (userPlan === 'usuario' && requiredPlan.includes('pro')) {
      return 'pro';
    }
    
    // Por defecto, sugerir el plan más alto
    return requiredPlan[requiredPlan.length - 1];
  };

  const handleUpgrade = () => {
    const suggestedPlan = getSuggestedPlan();
    const upgradeUrl = suggestedPlan
      ? `/upgrade-plan?upgrade=${suggestedPlan}`
      : '/upgrade-plan';
    router.push(upgradeUrl);
  };

  const handleBack = () => {
    router.back();
  };

  const handleGoToApp = () => {
    router.push('/app');
  };

  const getIcon = () => {
    return (
      <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m0 0v2m0-2h2m-2 0H10m2-5V9m0 0V7m0 2h2m-2 0H10" />
      </svg>
    );
  };

  const renderContent = () => (
    <>
      {/* Icon */}
      <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
        {getIcon()}
      </div>

      {/* Title */}
      <h3 className="text-xl font-semibold text-gray-800 mb-3 text-center">
        {title}
      </h3>

      {/* Message */}
      <p className="text-gray-600 mb-4 text-center">
        {getDefaultMessage()}
      </p>

      {/* Feature Info */}
      {feature && (
        <div className="bg-gray-50 rounded-lg p-3 mb-4">
          <p className="text-sm text-gray-700">
            <strong>Función:</strong> {feature}
          </p>
        </div>
      )}

      {/* Plan Info */}
      {(userPlan || requiredPlan.length > 0) && (
        <div className="bg-blue-50 rounded-lg p-4 mb-4">
          {userPlan && (
            <p className="text-blue-800 text-sm mb-2">
              <strong>Tu plan actual:</strong> {getPlanDisplayName(userPlan)}
            </p>
          )}
          {requiredPlan.length > 0 && (
            <p className="text-blue-800 text-sm">
              <strong>Planes requeridos:</strong> {requiredPlan.map(getPlanDisplayName).join(', ')}
            </p>
          )}
        </div>
      )}

      {/* Actions */}
      {customActions ? (
        customActions
      ) : (
        <div className="space-y-3">
          {showUpgradeButton && requiredPlan.length > 0 && (
            <button
              onClick={handleUpgrade}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors font-medium"
            >
              {getSuggestedPlan() 
                ? `Actualizar a ${getPlanDisplayName(getSuggestedPlan()!)}`
                : 'Ver Planes'
              }
            </button>
          )}
          
          {showBackButton && (
            <button
              onClick={handleBack}
              className="w-full bg-gray-200 text-gray-800 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors"
            >
              Volver
            </button>
          )}
          
          <button
            onClick={handleGoToApp}
            className="w-full text-blue-600 hover:text-blue-700 transition-colors text-sm"
          >
            Ir a la aplicación
          </button>
        </div>
      )}
    </>
  );

  // Renderizado según variante
  switch (variant) {
    case 'inline':
      return (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg className="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3 flex-1">
              <h4 className="text-sm font-medium text-red-800">{title}</h4>
              <p className="text-sm text-red-700 mt-1">{getDefaultMessage()}</p>
              {showUpgradeButton && requiredPlan.length > 0 && (
                <button
                  onClick={handleUpgrade}
                  className="mt-2 text-sm bg-red-100 text-red-800 px-3 py-1 rounded hover:bg-red-200 transition-colors"
                >
                  Actualizar Plan
                </button>
              )}
            </div>
          </div>
        </div>
      );

    case 'modal':
      return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg shadow-xl p-6 max-w-md w-full">
            {renderContent()}
          </div>
        </div>
      );

    case 'card':
    default:
      return (
        <div className="bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto">
          {renderContent()}
        </div>
      );
  }
}

// Componente específico para límites de tokens
export function TokenLimitReached({
  currentTokens,
  tokenLimit,
  userPlan,
  onUpgrade
}: {
  currentTokens: number;
  tokenLimit: number;
  userPlan: string;
  onUpgrade?: () => void;
}) {
  const percentage = tokenLimit > 0 ? Math.round((currentTokens / tokenLimit) * 100) : 0;
  
  return (
    <UnauthorizedAccess
      title="Límite de Tokens Alcanzado"
      message={`Has utilizado ${currentTokens.toLocaleString()} de ${tokenLimit.toLocaleString()} tokens este mes (${percentage}%).`}
      userPlan={userPlan}
      requiredPlan={userPlan === 'free' ? ['usuario'] : ['pro']}
      customActions={
        <div className="space-y-3">
          <div className="bg-yellow-50 rounded-lg p-3 mb-4">
            <div className="flex justify-between text-sm text-yellow-800 mb-2">
              <span>Uso mensual</span>
              <span>{percentage}%</span>
            </div>
            <div className="w-full bg-yellow-200 rounded-full h-2">
              <div 
                className="bg-yellow-600 h-2 rounded-full" 
                style={{ width: `${Math.min(percentage, 100)}%` }}
              ></div>
            </div>
          </div>
          
          {onUpgrade && (
            <button
              onClick={onUpgrade}
              className="w-full bg-yellow-600 text-white py-2 px-4 rounded-md hover:bg-yellow-700 transition-colors font-medium"
            >
              Ampliar Límite
            </button>
          )}
          
          <p className="text-xs text-gray-500 text-center">
            Los tokens se resetean el primer día de cada mes
          </p>
        </div>
      }
      variant="card"
    />
  );
}

// Helper function para mostrar nombres de planes
function getPlanDisplayName(plan: string): string {
  const names: Record<string, string> = {
    'free': 'Gratis',
    'usuario': 'Usuario',
    'pro': 'Pro'
  };
  return names[plan] || plan;
}
